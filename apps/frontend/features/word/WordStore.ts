/**
 * 词库管理状态存储
 * 🎯 核心价值：统一的词库状态管理，支持持久化和实时同步
 * 📦 功能范围：词库状态、词语管理、填词模式、数据持久化
 * 🔄 架构设计：基于Zustand的响应式状态管理，支持本地存储
 */

import { enableMapSet, produce } from 'immer';
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import type { BasicColorType, DataLevel } from '../matrix/MatrixTypes';
import type {
  WordEntry,
  WordInputState,
  WordLibrary,
  WordLibraryKey,
  WordLibraryState,
  WordValidationResult
} from './WordTypes';

import {
  createInitialWordLibraryState,
  createWordEntry,
  updateDuplicateWordsMap
} from './WordHook';

import { assignWordHighlightColor } from './WordConfig';
import { createWordLibraryKey } from './WordTypes';

// 启用 Immer 的 MapSet 插件
enableMapSet();

// ===== Store 接口定义 =====

interface WordStore extends WordLibraryState {
  // ===== 词库管理 =====

  /** 添加词语到指定词库 */
  addWord: (libraryKey: WordLibraryKey, text: string) => WordValidationResult;

  /** 从词库中删除词语 */
  removeWord: (libraryKey: WordLibraryKey, wordId: string) => boolean;

  /** 切换词库折叠状态 */
  toggleLibraryCollapse: (libraryKey: WordLibraryKey) => void;

  // ===== 词语查询 =====

  /** 获取词库 */
  getLibrary: (libraryKey: WordLibraryKey) => WordLibrary | undefined;

  // ===== 验证和检测 =====

  /** 输入验证（阻止性）- 用于UI层验证 */
  validateInput: (libraryKey: WordLibraryKey, text: string) => { isValid: boolean; errors: string[] };

  /** 检测跨词库重复（提醒性） */
  checkCrossLibraryDuplicate: (text: string) => { isDuplicate: boolean; duplicateLibraries: WordLibraryKey[] };

  /** 更新重复词语映射 */
  updateDuplicateMap: () => void;

  /** 获取词语高亮颜色 */
  getWordHighlightColor: (word: string) => string | undefined;

  /** 设置词语高亮颜色 */
  setWordHighlightColor: (word: string, color: string) => void;

  // ===== 数据管理 =====

  /** 导出词库数据 */
  exportData: () => string;

  /** 导入词库数据 */
  importData: (data: string) => boolean;

  /** 重置所有词库 */
  resetAllLibraries: () => void;
}

// ===== 填词模式状态管理 =====

interface WordInputStore extends WordInputState {
  /** 激活填词模式 */
  activateWordInput: (x: number, y: number, color: BasicColorType, level: DataLevel, boundWordId?: string) => Promise<void>;

  /** 退出填词模式 */
  deactivateWordInput: () => void;

  /** 选择下一个词语 */
  selectNextWord: () => void;

  /** 选择上一个词语 */
  selectPreviousWord: () => void;

  /** 确认选择当前词语 */
  confirmWordSelection: () => WordEntry | null;


}

// ===== 创建词库管理Store =====

export const useWordStore = create<WordStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      ...createInitialWordLibraryState(),

      // ===== 词库管理方法 =====

      addWord: (libraryKey: WordLibraryKey, text: string) => {
        // 使用新的输入验证（阻止性）
        const inputValidation = get().validateInput(libraryKey, text);

        if (!inputValidation.isValid) {
          // 输入验证失败，直接返回错误，不录入数据
          return {
            isValid: false,
            errors: inputValidation.errors,
            isDuplicate: false,
            duplicateLibraries: []
          };
        }

        // 输入验证通过，直接录入数据
        const trimmedText = text.trim();
        let addedSuccessfully = false;

        set(produce((draft) => {
          const library = draft.libraries.get(libraryKey);
          if (library) {
            const wordEntry = createWordEntry(trimmedText, library.color, library.level);
            library.words.push(wordEntry);
            library.lastUpdated = new Date();

            // 同步更新全局索引
            const { addWordToGlobalIndex } = require('./WordHook');
            addWordToGlobalIndex(draft.globalWordIndex, trimmedText, libraryKey);

            addedSuccessfully = true;
          }
        }));

        if (addedSuccessfully) {
          // 检查跨词库重复并更新高亮
          const crossLibraryCheck = get().checkCrossLibraryDuplicate(trimmedText);

          if (crossLibraryCheck.isDuplicate) {
            // 为跨词库重复词语分配高亮颜色
            const currentColor = get().getWordHighlightColor(trimmedText);
            if (!currentColor) {
              const newColor = assignWordHighlightColor(get().usedHighlightColors);
              get().setWordHighlightColor(trimmedText, newColor);
            }
          }

          // 更新重复词语映射
          get().updateDuplicateMap();

          return {
            isValid: true,
            errors: [],
            isDuplicate: crossLibraryCheck.isDuplicate,
            duplicateLibraries: crossLibraryCheck.duplicateLibraries
          };
        }

        return {
          isValid: false,
          errors: ['添加词语失败'],
          isDuplicate: false,
          duplicateLibraries: []
        };
      },

      removeWord: (libraryKey: WordLibraryKey, wordId: string) => {
        let removed = false;
        let removedWordText = '';

        set(produce((draft) => {
          const library = draft.libraries.get(libraryKey);
          if (library) {
            const wordIndex = library.words.findIndex((word: WordEntry) => word.id === wordId);
            if (wordIndex !== -1) {
              removedWordText = library.words[wordIndex].text;
              library.words.splice(wordIndex, 1);
              library.lastUpdated = new Date();

              // 同步更新全局索引
              const { removeWordFromGlobalIndex } = require('./WordHook');
              removeWordFromGlobalIndex(draft.globalWordIndex, removedWordText, libraryKey);

              removed = true;
            }
          }
        }));

        if (removed) {
          get().updateDuplicateMap();
        }

        return removed;
      },



      toggleLibraryCollapse: (libraryKey: WordLibraryKey) => {
        set(produce((draft) => {
          const library = draft.libraries.get(libraryKey);
          if (library) {
            library.collapsed = !library.collapsed;
          }
        }));
      },

      // ===== 查询方法 =====

      getLibrary: (libraryKey: WordLibraryKey) => {
        const state = get();
        // 安全检查：确保libraries是Map对象
        if (!(state.libraries instanceof Map)) {
          console.warn('⚠️ libraries不是Map对象，正在重新初始化...');
          // 重新初始化状态
          const defaultState = createInitialWordLibraryState();
          set(defaultState);
          return defaultState.libraries.get(libraryKey);
        }
        return state.libraries.get(libraryKey);
      },

      // ===== 验证方法 =====

      validateInput: (libraryKey: WordLibraryKey, text: string) => {
        const state = get();
        // 导入验证函数
        const { validateInputBeforeSubmit } = require('./WordHook');
        return validateInputBeforeSubmit(text, libraryKey, state.globalWordIndex);
      },

      checkCrossLibraryDuplicate: (text: string) => {
        const state = get();
        // 导入检测函数
        const { detectCrossLibraryDuplicates } = require('./WordHook');
        return detectCrossLibraryDuplicates(text, state.globalWordIndex);
      },

      updateDuplicateMap: () => {
        const state = get();

        // 使用增强版函数同时更新重复词语映射和高亮颜色
        const { updateDuplicateWordsMapWithColors } = require('./WordHook');
        const result = updateDuplicateWordsMapWithColors(
          state.libraries,
          state.wordHighlightColors,
          state.usedHighlightColors
        );

        set({
          duplicateWords: result.duplicateWords,
          wordHighlightColors: result.wordHighlightColors,
          usedHighlightColors: result.usedHighlightColors
        });
      },

      // ===== 数据管理方法 =====

      exportData: () => {
        const state = get();
        const exportData = {
          libraries: Array.from(state.libraries.entries()),
          lastSyncTime: state.lastSyncTime,
          exportTime: new Date()
        };
        return JSON.stringify(exportData, null, 2);
      },

      importData: (data: string) => {
        try {
          const importData = JSON.parse(data);
          const libraries = new Map(importData.libraries) as Map<WordLibraryKey, WordLibrary>;

          set({
            libraries,
            lastSyncTime: new Date(),
            duplicateWords: updateDuplicateWordsMap(libraries)
          });

          return true;
        } catch (error) {
          console.error('导入数据失败:', error);
          return false;
        }
      },

      resetAllLibraries: () => {
        set(createInitialWordLibraryState());

        // 清理所有单元格词语绑定
        try {
          const { useMatrixStore } = require('../matrix/MatrixStore');
          const matrixStore = useMatrixStore.getState();
          matrixStore.clearAllWordBindings();
        } catch (error) {
          console.warn('清理单元格绑定时出错:', error);
        }
      },

      // ===== 随机颜色管理方法 =====

      getWordHighlightColor: (word: string) => {
        const state = get();
        return state.wordHighlightColors.get(word);
      },

      setWordHighlightColor: (word: string, color: string) => {
        set(produce((draft) => {
          draft.wordHighlightColors.set(word, color);
          draft.usedHighlightColors.add(color);
        }));
      }
    }),
    {
      name: 'word-library-storage',
      version: 2, // 增加版本号以触发数据迁移
      partialize: (state) => ({
        libraries: Array.from(state.libraries.entries()),
        duplicateWords: Array.from(state.duplicateWords.entries()),
        wordHighlightColors: Array.from(state.wordHighlightColors.entries()),
        usedHighlightColors: Array.from(state.usedHighlightColors),
        globalWordIndex: Array.from(state.globalWordIndex.entries()).map(([word, libraries]) => [word, Array.from(libraries)]),
        isLoading: state.isLoading,
        lastSyncTime: state.lastSyncTime
      }),
      onRehydrateStorage: () => (state) => {
        if (state) {
          try {
            // 处理libraries字段
            if (Array.isArray(state.libraries)) {
              // 新格式：数组格式
              state.libraries = new Map(state.libraries as any);
            } else if (state.libraries && typeof state.libraries === 'object' && !(state.libraries instanceof Map)) {
              // 旧格式：普通对象格式，需要转换
              console.log('🔄 检测到旧版本数据格式，正在迁移...');
              const entries = Object.entries(state.libraries);
              state.libraries = new Map(entries as any);
            } else if (!state.libraries) {
              // 没有数据，使用默认值
              state.libraries = new Map();
            }

            // 处理duplicateWords字段
            if (Array.isArray(state.duplicateWords)) {
              state.duplicateWords = new Map(state.duplicateWords as any);
            } else if (state.duplicateWords && typeof state.duplicateWords === 'object' && !(state.duplicateWords instanceof Map)) {
              const entries = Object.entries(state.duplicateWords);
              state.duplicateWords = new Map(entries as any);
            } else if (!state.duplicateWords) {
              state.duplicateWords = new Map();
            }

            // 处理wordHighlightColors字段
            if (Array.isArray(state.wordHighlightColors)) {
              state.wordHighlightColors = new Map(state.wordHighlightColors as any);
            } else if (state.wordHighlightColors && typeof state.wordHighlightColors === 'object' && !(state.wordHighlightColors instanceof Map)) {
              const entries = Object.entries(state.wordHighlightColors);
              state.wordHighlightColors = new Map(entries as any);
            } else if (!state.wordHighlightColors) {
              state.wordHighlightColors = new Map();
            }

            // 处理usedHighlightColors字段
            if (Array.isArray(state.usedHighlightColors)) {
              state.usedHighlightColors = new Set(state.usedHighlightColors as any);
            } else if (state.usedHighlightColors && typeof state.usedHighlightColors === 'object' && !(state.usedHighlightColors instanceof Set)) {
              // 如果是对象格式，尝试获取值
              const values = Object.values(state.usedHighlightColors);
              state.usedHighlightColors = new Set(values as any);
            } else if (!state.usedHighlightColors) {
              state.usedHighlightColors = new Set();
            }

            // 处理globalWordIndex字段
            if (Array.isArray(state.globalWordIndex)) {
              // 新格式：数组格式 [[word, [library1, library2]], ...]
              state.globalWordIndex = new Map(
                (state.globalWordIndex as any).map(([word, libraries]: [string, string[]]) => [
                  word,
                  new Set(libraries)
                ])
              );
            } else if (!state.globalWordIndex) {
              // 没有索引数据，从现有词库重建
              console.log('🔄 重建全局词语索引...');
              const { buildGlobalWordIndex } = require('./WordHook');
              state.globalWordIndex = buildGlobalWordIndex(state.libraries);
            }

            console.log('✅ 数据迁移完成');
          } catch (error) {
            console.error('❌ 数据迁移失败，使用默认状态:', error);
            // 如果迁移失败，重置为默认状态
            const defaultState = createInitialWordLibraryState();
            Object.assign(state, defaultState);
          }
        }
      }
    }
  )
);

// ===== 创建填词模式Store =====

export const useWordInputStore = create<WordInputStore>((set, get) => ({
  // 初始状态
  isActive: false,
  selectedCell: null,
  matchedLibrary: null,
  selectedWordIndex: 0,
  availableWords: [],
  temporaryWord: null,
  isWordBound: false,

  activateWordInput: async (x: number, y: number, color: BasicColorType, level: DataLevel, boundWordId?: string) => {
    const libraryKey = createWordLibraryKey(color, level);
    const wordStore = useWordStore.getState();
    const library = wordStore.getLibrary(libraryKey);

    let selectedIndex = 0;
    let temporaryWord = library?.words?.[0]?.text || null;
    let isWordBound = false;

    // 如果传递了绑定的词语ID，定位到该词语
    if (boundWordId && library?.words) {
      const boundWordIndex = library.words.findIndex(word => word.id === boundWordId);
      if (boundWordIndex !== -1) {
        selectedIndex = boundWordIndex;
        temporaryWord = library.words[boundWordIndex].text;
        isWordBound = true;
      }
    }

    set({
      isActive: true,
      selectedCell: { x, y },
      matchedLibrary: libraryKey,
      selectedWordIndex: selectedIndex,
      availableWords: library?.words || [],
      temporaryWord,
      isWordBound
    });
  },

  deactivateWordInput: () => {
    set({
      isActive: false,
      selectedCell: null,
      matchedLibrary: null,
      selectedWordIndex: 0,
      availableWords: [],
      temporaryWord: null,
      isWordBound: false
    });
  },

  selectNextWord: () => {
    const state = get();
    if (state.availableWords.length > 0) {
      const nextIndex = (state.selectedWordIndex + 1) % state.availableWords.length;
      const nextWord = state.availableWords[nextIndex];
      set({
        selectedWordIndex: nextIndex,
        temporaryWord: nextWord?.text || null
      });
    }
  },

  selectPreviousWord: () => {
    const state = get();
    if (state.availableWords.length > 0) {
      const prevIndex = state.selectedWordIndex === 0
        ? state.availableWords.length - 1
        : state.selectedWordIndex - 1;
      const prevWord = state.availableWords[prevIndex];
      set({
        selectedWordIndex: prevIndex,
        temporaryWord: prevWord?.text || null
      });
    }
  },

  confirmWordSelection: () => {
    const state = get();
    if (state.availableWords.length > 0 && state.selectedWordIndex < state.availableWords.length) {
      const selectedWord = state.availableWords[state.selectedWordIndex];

      // 更新词语使用统计
      if (state.matchedLibrary && state.selectedCell) {
        // 这里可以添加使用统计更新逻辑
        // const wordStore = useWordStore.getState();
      }

      // 标记为已绑定
      set({ isWordBound: true });
      return selectedWord;
    }
    return null;
  },


}));
